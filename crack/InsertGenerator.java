import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Base64;
import java.util.Scanner;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.crypto.spec.GCMParameterSpec;

/**
 * 用于生成'5c'行insert语句的工具类
 * 基于LicTrialInstaller.java中的setDeployTime方法逻辑
 */
public class InsertGenerator {
    
    /**
     * AES加密密钥（来自LicTrialInstaller.java）
     */
    private static final String AES_KEY = "8D0/0GnGvIy8FFAc0g+vgw==";
    
    /**
     * 使用AES/GCM/NoPadding模式加密时间戳（用于ext2字段）
     * @param dateStr 时间戳字符串
     * @return 加密后的Base64字符串
     */
    public static String encryptAESGCM(String dateStr) {
        try {
            SecretKey secretKey = new SecretKeySpec(Base64.getDecoder().decode(AES_KEY), "AES");
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            // 使用与LicControlManagerImpl相同的固定IV
            byte[] iv = new byte[]{-34, -83, -66, -17, -54, -2, -70, -66, 18, 52, 86, 120};
            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(128, iv);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, gcmParameterSpec);
            byte[] cipherByte = cipher.doFinal(dateStr.getBytes("utf-8"));
            return Base64.getEncoder().encodeToString(cipherByte);
        } catch (Exception e) {
            throw new RuntimeException("AES/GCM加密失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成'5c'行的insert语句
     * @param dateStr 指定的时间戳字符串，如果为null则使用当前时间
     * @return 完整的insert语句字符串
     */
    public static String generateInsertStatement(String dateStr) {
        try {
            // 如果没有指定时间，使用当前时间戳
            if (dateStr == null) {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
                dateStr = simpleDateFormat.format(new Date());
            }

            // AES加密时间戳（ext1字段）
            SecretKey secretKey = new SecretKeySpec(Base64.getDecoder().decode(AES_KEY), "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            byte[] data = dateStr.getBytes();
            byte[] cipherByte = cipher.doFinal(data);
            String encryptedDateStr = Base64.getEncoder().encodeToString(cipherByte);

            // AES/GCM加密时间戳（ext2字段）
            String encryptedDateStrGCM = encryptAESGCM(dateStr);

            // 生成insert语句（参照LicTrialInstaller.java第119行，但使用正确的ext2值）
            String insertSql = "insert into gsppubextconfig(id,code,createdby,createdon,ext1,ext2,ext3,ext4,lastchangedby,lastchangedon) values('5c','pkp','client001',CURRENT_DATE,'" + encryptedDateStr + "','" + encryptedDateStrGCM + "','hfueiwhfewhfie','iewojfioewjfjj','client002',CURRENT_DATE)";

            return insertSql;

        } catch (Exception e) {
            throw new RuntimeException("生成insert语句失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成'5c'行的insert语句（使用当前时间）
     * @return 完整的insert语句字符串
     */
    public static String generateInsertStatement() {
        return generateInsertStatement(null);
    }
    
    /**
     * 解密AES加密的字符串
     * @param encryptedStr 加密的字符串
     * @return 解密后的字符串
     */
    public static String decryptAES(String encryptedStr) {
        try {
            SecretKey secretKey = new SecretKeySpec(Base64.getDecoder().decode(AES_KEY), "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedStr));
            return new String(decryptedBytes);
        } catch (Exception e) {
            throw new RuntimeException("解密失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解密AES/GCM加密的字符串（用于ext2字段）
     * @param encryptedStr 加密的字符串
     * @return 解密后的字符串
     */
    public static String decryptAESGCM(String encryptedStr) {
        try {
            SecretKey secretKey = new SecretKeySpec(Base64.getDecoder().decode(AES_KEY), "AES");
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            byte[] iv = new byte[]{-34, -83, -66, -17, -54, -2, -70, -66, 18, 52, 86, 120};
            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(128, iv);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, gcmParameterSpec);
            byte[] cipherByte = cipher.doFinal(Base64.getDecoder().decode(encryptedStr));
            return new String(cipherByte, "utf-8");
        } catch (Exception e) {
            throw new RuntimeException("AES/GCM解密失败: " + e.getMessage(), e);
        }
    }

    /**
     * 主方法，用于测试和输出insert语句
     */
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);

        try {
            System.out.println("=== GSCloud '5c'行 Insert语句生成器 ===");
            System.out.println("1. 使用当前时间生成");
            System.out.println("2. 输入指定时间生成");
            System.out.println("3. 验证数据库时间戳");
            System.out.print("请选择操作 (1/2/3): ");

            String choice = scanner.nextLine().trim();

            switch (choice) {
                case "1":
                    // 使用当前时间
                    String insertStatement = generateInsertStatement();
                    System.out.println("\n生成的insert语句：");
                    System.out.println(insertStatement);
                    break;

                case "2":
                    // 用户输入时间
                    System.out.print("请输入时间戳 (格式: yyyy/MM/dd HH:mm:ss): ");
                    String userTime = scanner.nextLine().trim();
                    String userInsertStatement = generateInsertStatement(userTime);
                    System.out.println("\n生成的insert语句：");
                    System.out.println(userInsertStatement);
                    break;

                case "3":
                    // 验证数据库时间戳
                    System.out.println("\n=== 验证数据库时间戳 ===");
                    String dbTime = "2025/07/25 08:10:36";
                    System.out.println("数据库时间戳: " + dbTime);

                    // 生成对应的密文
                    String testInsert = generateInsertStatement(dbTime);
                    System.out.println("\n使用该时间戳生成的insert语句：");
                    System.out.println(testInsert);

                    // 提取生成的密文
                    String[] parts = testInsert.split("'");
                    String generatedExt1 = parts[7]; // ext1字段 (第4个单引号对)
                    String generatedExt2 = parts[9]; // ext2字段 (第5个单引号对)

                    // 数据库中的实际值
                    String dbExt1 = "fwdk+wlqQrmg2mp2yudVZVyByl0GRrXgtq2Upl9u31c=";
                    String dbExt2 = "VN0ijAr5+ckaycOgUHtD+XlM0esOM6mNWCInQKQqeewSacQ=";

                    System.out.println("\n=== 对比结果 ===");
                    System.out.println("ext1对比:");
                    System.out.println("  数据库值: " + dbExt1);
                    System.out.println("  生成值:   " + generatedExt1);
                    System.out.println("  是否一致: " + dbExt1.equals(generatedExt1));

                    System.out.println("\next2对比:");
                    System.out.println("  数据库值: " + dbExt2);
                    System.out.println("  生成值:   " + generatedExt2);
                    System.out.println("  是否一致: " + dbExt2.equals(generatedExt2));

                    // 解密验证
                    System.out.println("\n=== 解密验证 ===");
                    try {
                        String decryptedDbExt1 = decryptAES(dbExt1);
                        String decryptedDbExt2 = decryptAESGCM(dbExt2);
                        System.out.println("数据库ext1解密: " + decryptedDbExt1);
                        System.out.println("数据库ext2解密: " + decryptedDbExt2);
                    } catch (Exception e) {
                        System.out.println("解密失败: " + e.getMessage());
                    }
                    break;

                default:
                    System.out.println("无效选择！");
                    break;
            }

        } catch (Exception e) {
            System.err.println("错误: " + e.getMessage());
            e.printStackTrace();
        } finally {
            scanner.close();
        }
    }
}
